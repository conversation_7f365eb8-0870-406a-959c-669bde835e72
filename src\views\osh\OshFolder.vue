<template>
  <VRow class="flex-column" dense>
    <VCol class="flex-grow-0">
      <OshHeaderSummaryDisplayValue
        v-if="oshFolderSummary.value"
        :summary="oshFolderSummary"
        :selections
        :filtered-summary="filteredOshFolderSummary"
      />
    </VCol>
    <VCol class="flex-grow-0">
      <VRow dense>
        <VCol>
          <SearchInput
            :loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="
              (v) => {
                updateFilterByFieldname('search', v)
              }
            "
          />
        </VCol>
        <VCol v-show="!isEqual(defaultPageFilter, pageFilter)" align-self="center">
          <VLink icon="mdi-sync" @click="pageFilter = defaultPageFilter"> Réinitialiser les filtres </VLink>
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn v-show="!filterDrawer" @click="emit('click:custom')">Personnaliser</NjBtn>
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn :loading="exportOperationsLoading" @click="exportOperations">Exporter</NjBtn>
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn
            v-show="!filterDrawer"
            @click="
              () => {
                filterDrawer = !filterDrawer
                onClickRow(undefined)
              }
            "
            >Filtres</NjBtn
          >
        </VCol>
        <VCol v-if="userStore.hasRole('ADMIN_PLUS')" class="flex-grow-0">
          <NjBtn @click="emptyFolder"> Sortir du dossier </NjBtn>
          <AlertDialog
            style="max-width: 600px"
            v-bind="emptyFolderDialog.props"
            title="Sortir du dossier"
            max-width="640px"
          >
            Vous allez sortir du dossier
            {{ selections.length == 0 ? data.value?.totalElements : selections.length }} opérations ?

            <template #positiveButton>
              <NjBtn @click="emptyFolderDialog.props['onClick:positive']"> Valider </NjBtn>
            </template>
          </AlertDialog>
        </VCol>
        <VCol v-if="userStore.hasRole('ADMIN_PLUS')" class="flex-grow-0" align-self="center">
          <NjBtn @click="setPriority">Affecter à un dossier</NjBtn>
          <AlertDialog
            style="max-width: 600px"
            v-bind="setPriorityDialog.props"
            title="Affecter à un dossier"
            max-width="640px"
          >
            <VRow class="flex-column" dense>
              <VCol>
                A quelle dossier souhaiter vous affecter les
                {{ selections.length == 0 ? data.value?.totalElements : selections.length }}
                opérations ?
              </VCol>
              <VCol>
                <VSelect
                  v-model="selectedOshPriority"
                  style="max-width: 200px"
                  label="Dossier"
                  :items="oshPriorities"
                  item-title="label"
                  item-value="value"
                  clearable
                />
              </VCol>
            </VRow>

            <template #positiveButton>
              <NjBtn :disabled="!selectedOshPriority" @click="setPriorityDialog.props['onClick:positive']">
                Valider
              </NjBtn>
            </template>
          </AlertDialog>
        </VCol>
      </VRow>
    </VCol>
    <VCol class="content-layout content-layout__main">
      <NjDataTable
        v-model:selections="selections"
        :pageable="pageable"
        class="content-layout content-layout__main"
        fixed-header
        fixed
        :headers="headers"
        :page="data.value"
        :loading="data.loading"
        multi-selection
        checkboxes
        :on-click-row="
          (row) => {
            filterDrawer = false
            onClickRow(row.id)
          }
        "
        :disabled-row="(row: OperationExportResultDto) => ['IMPROPER', 'AGENCY_GIVE_UP'].includes(row.oshCommentary)"
        @update:pageable="updatePageable"
      >
        <template #[`item.oshCommentary`]="{ item }">
          <template v-if="userStore.hasRole('ADMIN_PLUS', 'SIEGE', 'INSTRUCTEUR', 'ADMIN')">
            <VSelect
              :loading="item.id === currentOperationForMessage?.id"
              :model-value="item.oshCommentary"
              style="min-width: 330px"
              :items="oshCommentaryLabel"
              item-title="label"
              item-value="value"
              clearable
              @update:model-value="updateOshCommentary(item, $event)"
              @click.stop
            />
          </template>
          <span v-else>{{ oshCommentaryLabel.find((i) => i.value == item.oshCommentary)?.label }}</span>
        </template>

        <template #[`item.stepId`]="{ item }">
          <s v-if="['IMPROPER', 'AGENCY_GIVE_UP'].includes(item.oshCommentary)"> {{ item.formatedStepName }} </s>
          <span v-else :class="item.oshCommentary == 'WAITING_AGENCY_RETURN' ? 'text-error' : 'text-black'">
            {{ item.formatedStepName }}
          </span>
        </template>
      </NjDataTable>
    </VCol>
    <FilterDrawer
      v-model:original-filter="pageFilter"
      v-model:valuation-mode="pageFilter.valuationMode"
      :model-value="filterDrawer"
      @update:model-value="filterDrawer = $event"
    />
    <CommentaireDialog
      ref="commentaireDialogRef"
      v-model="commentaireDialog.props.modelValue"
      v-model:mandatory-message="mandatoryMessage"
      :operation="currentOperationForMessage"
      :advised-recipient="advisedRecipient"
      :mandatory-concerned-document-type-ids
      @send="handleSentMessage"
      @close="commentaireDialog.props['onClick:negative']"
    />
  </VRow>
</template>
<script setup lang="ts">
import { makeEmptyFilter, operationApi, type OperationFilter } from '@/api/operation'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import SearchInput from '@/components/SearchInput.vue'
import { trace } from '@/stores/analytics'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { Operation } from '@/types/operation'
import type { OperationExportResultDto } from '@/types/operationExportResultDto'
import {
  oshCommentaryLabel,
  type OshCommentaryType,
  type OshFolder,
  type OshFolderSummary,
} from '@/types/osh/oshFolder'
import { isEmpty, isEqual, omitBy } from 'lodash'
import type { PropType } from 'vue'
import { useRoute } from 'vue-router'
import { VCol, VRow, VSelect, VSpacer } from 'vuetify/components'
import { useExportOperation } from '../exportOperation'
import CommentaireDialog from '../operation/dialog/CommentaireDialog.vue'
import FilterDrawer from '../operation/FilterDrawer.vue'
import OshHeaderSummaryDisplayValue from './OshHeaderSummaryDisplayValue.vue'

const props = defineProps({
  periodId: {
    type: Number,
    required: true,
  },
  oshPriority: {
    type: Number,
    required: true,
  },
  onClickRow: {
    type: Function as PropType<(id: number | undefined) => any>,
    default: () => {},
  },
  headers: {
    type: Array as PropType<DataTableHeader<OperationExportResultDto>[]>,
    default: () => [],
  },
  currentTab: {
    type: String,
    required: true,
  },
})
const emit = defineEmits<{
  'click:custom': []
}>()

const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const dialogStore = useDialogStore()
const defaultPageFilter = {
  ...makeEmptyFilter(),
  periodIds: [props.periodId],
  oshPriority: props.oshPriority,
}

const { data, pageable, updatePageable, reload, pageFilter, updateFilterByFieldname } = usePagination<
  OperationExportResultDto,
  OperationFilter
>(
  (filter, pageable) => operationApi.findAllExportOperationResultDto(filter, pageable),
  {
    ...makeEmptyFilter(),
    periodIds: [props.periodId],
    oshPriority: props.oshPriority,
  },
  {
    sort: ['dateStep50,DESC'],
  },
  {
    saveFiltersName: 'OshFolder-' + props.currentTab,
  }
)

const selections = ref<OperationExportResultDto[]>([])

const route = useRoute()
const emptyFolderDialog = useConfirmAlertDialog()
const emptyFolder = async () => {
  if (await emptyFolderDialog.confirm()) {
    let filter: OperationFilter = {}
    if (selections.value.length) {
      filter.operationIds = selections.value.map((i) => i.id)
    } else {
      filter = pageFilter.value
    }
    operationApi
      .updateOshPriority(filter, { oshPriority: null })
      .then(() => {
        trace('setOshPriority', {
          route: {
            name: route.name,
            params: route.params,
            fullpath: route.fullPath,
          },
          tab: props.currentTab,
          filter: omitBy(toRaw(filter), (it) => isEmpty(it) || it == null || it === false),
          oshPriority: null,
          selectionCount: selections.value.length || data.value.value?.totalElements,
        })
        selections.value = []
        reload()
        // loadSummary()
        loadOshFolderSummary()
      })
      .catch(async (err) => {
        snackbarStore.setError(await handleAxiosException(err))
        throw err
      })
  }
}

const oshFolder = ref(emptyValue<OshFolder>())
const oshFolderSummary = ref(emptyValue<OshFolderSummary>())
const filteredOshFolderSummary = ref(emptyValue<OshFolderSummary>())

const loadOshFolderSummary = () => {
  handleAxiosPromise(
    oshFolderSummary,
    oshFolderApi.getOneSummary(props.periodId, props.oshPriority, defaultPageFilter),
    {
      afterError: async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      },
    }
  )
}

const loadFilteredOshFolderSummary = () => {
  if (isEqual(defaultPageFilter, pageFilter.value)) {
    filteredOshFolderSummary.value = emptyValue()
  } else {
    handleAxiosPromise(
      filteredOshFolderSummary,
      oshFolderApi.getOneSummary(props.periodId, props.oshPriority, pageFilter.value),
      {
        afterError: async (e) => {
          snackbarStore.setError(await handleAxiosException(e))
        },
      }
    )
  }
}

onMounted(() => {
  handleAxiosPromise(oshFolder, oshFolderApi.getOne(props.periodId, props.oshPriority), {
    afterError: async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    },
  })
  loadOshFolderSummary()
  // loadSummary()
})

const filterDrawer = ref(false)

const currentOperationForMessage = shallowRef<Operation>()
const mandatoryMessage = ref('')
const updateOshCommentary = async (operationExportResultDto: OperationExportResultDto, event: OshCommentaryType) => {
  try {
    let needUpdate = false
    if (event === 'BACK_TO_STEP_50') {
      // previousStepLoading.value = true
      const o = (await simulationApi.findById(operationExportResultDto.id)).data
      currentOperationForMessage.value = o

      if (o.stepId == 50) {
        snackbarStore.setError("L'opération est déjà à l'étape 50")
        return
      }
      mandatoryMessage.value = ''
      advisedRecipient.value = (
        await userApi
          .getAll({ size: 1000 }, { active: true, roles: ['AGENCE_PLUS'], entityIds: [o.leadingEntity.id] })
          .catch((e) => {
            snackbarStore.setError(
              "Nous n'avons pas pu récupérer la liste des contacts pour le traitement de la simulation"
            )
            throw e
          })
      ).data.content
        .map((it) => it.email)
        .filter((it) => it)
        .sort() as string[]

      await commentaireDialog.confirm()
      needUpdate = true
    } else if (event === 'IMPROPER') {
      if (
        !(await dialogStore.addAlert({
          title: "Annulation de l'opération",
          message: "Êtes-vous sûr de vouloir passer l'opération en Non Conforme ?",
          maxWidth: '640px',
        }))
      ) {
        return
      }
      const o = (await simulationApi.findById(operationExportResultDto.id)).data
      currentOperationForMessage.value = o

      const mailOperation: Operation = {
        ...o,
        classicCumac: 0,
        precariousnessCumac: 0,
        status: 'IMPROPER',
      }

      mandatoryMessage.value = dafMail(mailOperation, false)
      advisedRecipient.value = mailOperation!.entity.entityDetails.effectiveDafMail

      const result = await commentaireDialog.confirm()
      if (!result) {
        currentOperationForMessage.value = undefined
        return
      }
      await operationApi.updateStatus(operationExportResultDto.id, 'IMPROPER')
      needUpdate = true
    } else if (event === 'AGENCY_GIVE_UP') {
      if (
        !(await dialogStore.addAlert({
          title: "Abandon de l'opération",
          message: "Êtes-vous sûr de vouloir passer l'opération en abandon ?",
          maxWidth: '640px',
        }))
      ) {
        return
      }

      const o = (await simulationApi.findById(operationExportResultDto.id)).data
      currentOperationForMessage.value = o

      const mailOperation: Operation = {
        ...o,
        classicCumac: 0,
        precariousnessCumac: 0,
        status: 'IMPROPER',
      }

      mandatoryMessage.value = dafMail(mailOperation, false)
      advisedRecipient.value = mailOperation!.entity.entityDetails.effectiveDafMail

      const result = await commentaireDialog.confirm()
      if (!result) {
        currentOperationForMessage.value = undefined
        return
      }

      await operationApi.updateStatus(operationExportResultDto.id, 'CANCELLED')
      needUpdate = true
    }

    await operationApi.updateOshCommentary(operationExportResultDto.id, event)

    if (needUpdate) {
      reload()
      loadOshFolderSummary()
      loadFilteredOshFolderSummary()
    } else {
      operationExportResultDto.oshCommentary = event
    }
  } catch (e: unknown) {
    snackbarStore.setError(
      "Une erreur s'est produit pendant la mise à jour des données : " + (await handleAxiosException(e))
    )
  }
  currentOperationForMessage.value = undefined
}
const commentaireDialog = useConfirmAlertDialog()
// const reloadMessages = ref(false)
const advisedRecipient = ref<string[]>([])
const mandatoryConcernedDocumentTypeIds = ref<number[]>()
const handleSentMessage = () => {
  // reloadMessages.value = true
  // operationEventHistoryRef.value?.reload()
  commentaireDialog.props['onClick:positive']()
}

const { exportOperationsLoading, exportOperations } = useExportOperation(pageFilter, selections, data, false)

const setPriorityDialog = useConfirmAlertDialog()
const selectedOshPriority = ref<number | null>(null)
const oshPriorities = [
  { value: 1, label: 'D1' },
  { value: 2, label: 'D2' },
  { value: 3, label: 'D3' },
  { value: 4, label: 'D4' },
]

const setPriority = async () => {
  selectedOshPriority.value = null
  if (await setPriorityDialog.confirm()) {
    let filter: OperationFilter = {}
    if (selections.value.length) {
      filter.operationIds = selections.value.map((i) => i.id)
    } else {
      filter = pageFilter.value
    }
    operationApi.updateOshPriority(filter, { oshPriority: selectedOshPriority.value! }).then(() => {
      trace('setOshPriority', {
        route: {
          name: route.name,
          params: toRaw(route.params),
          fullpath: route.fullPath,
        },
        tab: props.currentTab,
        filter: omitBy(toRaw(filter), (it) => isEmpty(it) || it == null || it === false),
        oshPriority: selectedOshPriority.value,
        selectionCount: selections.value.length || data.value.value?.totalElements,
      })
      selections.value = []
      reload()
      loadOshFolderSummary()
      loadFilteredOshFolderSummary()
    })
  }
}

watch(
  () => props.currentTab,
  (v, oldV) => {
    const value = `p${props.periodId}-d${props.oshPriority}`
    if (!isEqual(v, oldV)) {
      filterDrawer.value = false
      if (v === value) {
        reload()
        loadOshFolderSummary()
        loadFilteredOshFolderSummary()
      }
    }
  }
)
watch(
  pageFilter,
  () => {
    loadFilteredOshFolderSummary()
  },
  {
    deep: true,
  }
)
</script>
