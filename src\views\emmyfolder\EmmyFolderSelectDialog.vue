<template>
  <div>
    <CardDialog
      :model-value="modelValue"
      title="Préparer le dossier EMMY"
      positive-button="Suivant"
      @update:model-value="emits('update:model-value', $event)"
    >
      <VRow class="flex-column content-layout">
        <VCol>
          <VRow class="align-center">
            <VCol cols="6">
              <SearchInput :model-value="pageFilter.search" @update:model-value="updateSearch" />
            </VCol>
            <VCol>
              <VCheckbox
                v-if="userStore.isAdminPlus"
                v-model="showOnlyEmmyFoldersInStep80"
                label="Seulemement les dossiers à l'étape 80"
              />
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn @click="createEmmyFolder"> Créer </NjBtn>
            </VCol>
          </VRow>
        </VCol>
        <VCol class="content-layout">
          <NjDataTable
            class="content-layout_main"
            :headers="emmyHeader"
            :page="dataEmmy.value!"
            :pageable="pageableEmmy"
            :selections="mutableSelections"
            @update:pageable="updatePageableEmmy"
            @update:selections="mutableSelections = $event"
          />
        </VCol>
      </VRow>
      <template #actions>
        <NjBtn variant="outlined" @click="emits('update:model-value', false)">Annuler</NjBtn>
        <NjBtn @click="addOperationInEmmyFolder(mutableSelections[0])">Suivant</NjBtn>
      </template>
    </CardDialog>

    <EmmyFolderCreateDialog v-model="emmyFolderCreateDialog" @save:emmy-folder="addOperationInEmmyFolder" />
  </div>
</template>
<script setup lang="ts">
import type { EmmyFolderFilter } from '@/api/emmyFolder'
import type { EmmyFolder } from '@/types/emmyFolder'
import { emmyFolderApi } from '@/api/emmyFolder'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import EmmyFolderCreateDialog from './EmmyFolderCreateDialog.vue'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  modelValue: Boolean,
  operationId: {
    type: Number,
    required: true,
  },
  selections: Array as PropType<EmmyFolder[]>,
})

const emits = defineEmits<{
  'update:model-value': [boolean]
}>()

const dialogStore = useDialogStore()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()

const emmyHeader: DataTableHeader[] = [
  {
    title: 'Numéro dossier',
    value: 'emmyCode',
  },
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Nb. opérations',
    value: 'operationsCount',
  },
  {
    title: 'Etape',
    value: 'stepId',
  },
  {
    title: 'Responsable',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Validation Instruction',
    value: 'na1',
  },
  {
    title: 'Envoi PNCEE',
    value: 'na2',
  },
  {
    title: 'Délivrance PNCEE',
    value: 'na3',
  },
  {
    title: 'Nb. demandés kWhc',
    value: 'na4',
  },
  {
    title: 'Mnt. dem. €',
    value: 'na5',
  },
  {
    title: 'Réf. client',
    value: 'na6',
  },
]

const showOnlyEmmyFoldersInStep80 = ref(true)
watch(showOnlyEmmyFoldersInStep80, () => {
  reload()
})

const {
  data: dataEmmy,
  pageable: pageableEmmy,
  updatePageable: updatePageableEmmy,
  pageFilter,
  updateFilter,
  reload,
} = usePagination<EmmyFolder, EmmyFolderFilter>(
  (filter, pageable) =>
    emmyFolderApi.findAll(
      {
        ...filter,
        stepIds: showOnlyEmmyFoldersInStep80.value ? [80] : [80, 90, 100],
        myFolders: filter.myFolders ? true : undefined,
      },
      pageable
    ),
  {}
)

const updateSearch = (value: string) => {
  updateFilter({ search: value })
}

const emmyFolderCreateDialog = ref(false)
const createEmmyFolder = () => {
  emmyFolderCreateDialog.value = true
}

const addOperationInEmmyFolder = async (v: EmmyFolder) => {
  if (v.stepId !== 80) {
    if (
      !(await dialogStore.addAlert({
        title: "Ajouter une opération à un dossier EMMY au delà de l'étape 80",
        message:
          "Vous allez ajouter un dossier EMMY qui est au delà de l'étape 80.\nÊtes-vous sûr de vouloir continuer ?",
        maxWidth: 640,
      }))
    ) {
      return
    }
  }
  emmyFolderApi
    .addOperation(v.id, props.operationId)
    .then(() => {
      emits('update:model-value', false)
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}

const mutableSelections = ref<EmmyFolder[]>([])
</script>
