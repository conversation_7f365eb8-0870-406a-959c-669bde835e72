import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { EmmyFolder, EmmyFolderBatchSummary, EmmyFolderWithSummary } from '@/types/emmyFolder'
import type { LocalDate } from '@/types/date'
import type { Operation } from '@/types/operation'

const emmyFolderSubPartUri = '/emmy_folders'

export interface EmmyFolderRequest {
  name: string
  emmyCode: string
  stepId: number
  pnceeSubmissionDate: LocalDate | null
  pnceeIssuedDate: LocalDate | null
  pnceeClassicIssuedNumber: string | null
  pnceePrecariousnessIssuedNumber: string | null
}

export type EmmyFolderFilter = Partial<{
  myFolders: true | null
  search: string
  stepIds: number[]
  ids: number[]
  availableForSale: boolean
  periodIds: number[]
}>

export interface ExportBatchRequest {
  onlyNotValidatedInEmmy: boolean
}

export type BatchSummaryFilter = Partial<{
  onlyNotValidatedInEmmy: boolean
}>

class EmmyFolderApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: EmmyFolderRequest): AxiosPromise<EmmyFolder> {
    return this.axios.post(emmyFolderSubPartUri, request)
  }

  public save(id: number, request: EmmyFolderRequest): AxiosPromise<EmmyFolder> {
    return this.axios.put(emmyFolderSubPartUri + '/' + id, request)
  }

  public findAll(filter: EmmyFolderFilter, pageable: Pageable): AxiosPromise<Page<EmmyFolder>> {
    return this.axios.get(emmyFolderSubPartUri, {
      params: { ...filter, ...pageable },
    })
  }

  public findById(id: number): AxiosPromise<EmmyFolderWithSummary> {
    return this.axios.get(emmyFolderSubPartUri + '/' + id)
  }

  public delete(ids: number[]): AxiosPromise {
    return this.axios.delete(emmyFolderSubPartUri, {
      params: {
        ids,
      },
    })
  }

  public addOperation(emmyFolderId: number, operationId: number) {
    return this.axios.post(emmyFolderSubPartUri + '/' + emmyFolderId + '/operations', {
      operationId,
    })
  }

  public removeOperation(emmyFolderId: number, operationId: number): AxiosPromise<Operation> {
    return this.axios.delete(emmyFolderSubPartUri + '/' + emmyFolderId + '/operations/' + operationId)
  }

  public findAllBatchSummary(id: number, filter: BatchSummaryFilter): AxiosPromise<EmmyFolderBatchSummary[]> {
    return this.axios.get(emmyFolderSubPartUri + '/' + id + '/batch_summaries', {
      params: filter,
    })
  }

  public exportBatchToCSV(emmyFolderId: number, batchId: number, request: ExportBatchRequest) {
    return this.axios.post(
      emmyFolderSubPartUri + `/${emmyFolderId}/batches/${batchId}/export`,
      { ...request },
      {
        responseType: 'blob',
      }
    )
  }

  public validateBatch(emmyFolderId: number, batchId: number) {
    return this.axios.post(emmyFolderSubPartUri + `/${emmyFolderId}/batches/${batchId}/validate`)
  }
}

export const emmyFolderApi = new EmmyFolderApi(axiosInstance)
