<template>
  <CardDialog
    :width="'40%'"
    :model-value="modelValue"
    title="Nouveau Dossier EMMY"
    @update:model-value="(v) => emits('update:model-value', v)"
  >
    <VForm ref="formRef">
      <VRow class="flex-column mb-n2">
        <VCol>
          <VTextField v-model="form.name" label="Nom" :rules="[requiredRule]" />
        </VCol>
        <VCol>
          <VTextField v-model="form.emmyCode" label="Code EMMY" :rules="[requiredRule]" />
        </VCol>
      </VRow>
    </VForm>
    <template #actions>
      <VSpacer />
      <NjBtn variant="outlined" :disabled="emmyFolder.loading" @click="emits('update:model-value', false)">
        Annuler
      </NjBtn>
      <NjBtn color="primary" :loading="emmyFolder.loading" @click="createEmmyFolder"> Valider </NjBtn>
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import type { EmmyFolderRequest } from '@/api/emmyFolder'
import { useSnackbarStore } from '@/stores/snackbar'
import type { EmmyFolder } from '@/types/emmyFolder'
import type { VForm } from 'vuetify/components'
import { requiredRule } from '@/types/rule'
import type { PropType } from 'vue'
import { emmyFolderApi } from '@/api/emmyFolder'

const props = defineProps({
  modelValue: Boolean,
  afterSuccess: Function as PropType<(v: EmmyFolder) => any>,
})

const emits = defineEmits<{
  'update:model-value': [boolean]
  'save:emmy-folder': [EmmyFolder]
}>()

const snackbarStore = useSnackbarStore()

const formRef = ref<VForm | null>(null)
const form = ref<EmmyFolderRequest>({
  emmyCode: '',
  name: '',
  stepId: 80,
  pnceeIssuedDate: null,
  pnceeSubmissionDate: null,
  pnceeClassicIssuedNumber: null,
  pnceePrecariousnessIssuedNumber: null,
})

const emmyFolder = ref(emptyValue<EmmyFolder>())

const createEmmyFolder = async () => {
  const validate = await formRef.value!.validate()
  if (validate.valid) {
    const request: EmmyFolderRequest = form.value
    handleAxiosPromise(emmyFolder, emmyFolderApi.create(request), {
      afterError: () => snackbarStore.setError('Erreur lors de la création du dossier EMMY'),
      afterSuccess: (v) => {
        emits('save:emmy-folder', v.data)
      },
    })
  }
}

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      form.value = {
        emmyCode: '',
        name: '',
        stepId: 80,
        pnceeIssuedDate: null,
        pnceeSubmissionDate: null,
        pnceeClassicIssuedNumber: null,
        pnceePrecariousnessIssuedNumber: null,
      }
    }
  }
)
</script>
